import { GoogleGenerativeAI } from "@google/generative-ai";
import { DBOS } from "@dbos-inc/dbos-sdk";

export class LLM {
    @DBOS.step()
    static async generate(systemPrompt: string, userPrompt: string): Promise<any> {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
        const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

        const chat = model.startChat({
            history: [
                {
                    role: "user",
                    parts: [{ text: systemPrompt }],
                },
                {
                    role: "model",
                    parts: [{ text: "Okay, I'm ready. What's the user's request?" }],
                },
            ],
            generationConfig: {
                maxOutputTokens: 8192,
                responseMimeType: "application/json",
            },
        });

        const result = await chat.sendMessage(userPrompt);
        const response = result.response;
        const text = response.text();
        return JSON.parse(text);
    }
}