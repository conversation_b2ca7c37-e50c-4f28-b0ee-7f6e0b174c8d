import { DBOS, Workflow, Step, Transaction, TransactionContext, WorkflowContext, StepContext } from '@dbos-inc/dbos-sdk';
import { Knex } from 'knex';
import { LLM } from './llm';
import * as fs from 'fs/promises';
import * as path from 'path';

// Interface definitions remain the same...
interface Conversation {
    id: number;
    created_at: Date;
    updated_at: Date;
}

interface Message {
    id: number;
    character: string;
    text: string;
    conversation_id: number;
    created_at: Date;
    updated_at: Date;
}

export class ForaChat {

    @Transaction()
    static async createConversation(txn: TransactionContext<Knex>): Promise<Conversation> {
        const [conversation] = await txn.client<Conversation>('conversations').insert({}).returning('*');
        return conversation;
    }

    @Transaction()
    static async addMessage(txn: TransactionContext<Knex>, character: string, text: string, conversation_id: number): Promise<Message> {
        const [message] = await txn.client<Message>('messages').insert({
            character,
            text,
            conversation_id
        }).returning('*');
        return message;
    }

    @Step()
    static async getSystemPrompt(ctxt: StepContext): Promise<string> {
        const filePath = path.join(__dirname, '..', 'prompts', 'agent_system.md');
        return fs.readFile(filePath, 'utf-8');
    }

    @Workflow()
    static async chatWorkflow(ctxt: WorkflowContext, userMessage: string): Promise<any> {
        const conversation = await ctxt.invoke(ForaChat).createConversation();
        await ctxt.invoke(ForaChat).addMessage("user", userMessage, conversation.id);
        
        const systemPrompt = await ctxt.invoke(ForaChat).getSystemPrompt();
        const llmResponse = await ctxt.invoke(LLM).generate(systemPrompt, userMessage);

        const responseMessage = llmResponse.reply[0];
        await ctxt.invoke(ForaChat).addMessage(responseMessage.character, responseMessage.text, conversation.id);
        return responseMessage;
    }
}
