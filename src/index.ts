import { DBOS } from '@dbos-inc/dbos-sdk';
import express, { Request, Response } from 'express';
import { ForaChat } from './operations';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const app = express();
app.use(express.json());

app.post('/chat', (req: Request, res: Response) => {
    (async () => {
        try {
            const { text } = req.body;
            if (!text) {
                return res.status(400).json({ error: 'Request body must include "text"' });
            }
            const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow({} as any, text);
            const result = await handle.getResult();
            res.json(result);
        } catch (error) {
            console.error("Error in /chat handler:", error);
            const errorMessage = (error as Error).message;
            res.status(500).json({ error: "An internal server error occurred.", details: errorMessage });
        }
    })();
});

async function main() {
    DBOS.setConfig({
        "name": "forachat",
        "databaseUrl": process.env.DBOS_DATABASE_URL,
        "userDbclient": "knex"
    });
    await DBOS.launch();
    console.log("DBOS Launched.");

    const port = 3000;

    await new Promise<void>((resolve) => {
        app.listen(port, () => {
            console.log(`🚀 Server is running on http://localhost:${port}`);
            resolve();
        });
    });
}

main().catch((error) => {
    console.error('❌ Server failed to start:', error);
    process.exit(1);
});
