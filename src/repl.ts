import readline from 'readline';
import http from 'http';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: '> '
});

rl.prompt();

rl.on('line', (line) => {
  const postData = JSON.stringify({ text: line.trim() });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/chat',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      const response = JSON.parse(data);
      console.log(`${response.character}: ${response.text}`);
      rl.prompt();
    });
  });

  req.on('error', (e) => {
    console.error(`problem with request: ${e.message}`);
    rl.prompt();
  });

  req.write(postData);
  req.end();
}).on('close', () => {
  console.log('Have a great day!');
  process.exit(0);
});
